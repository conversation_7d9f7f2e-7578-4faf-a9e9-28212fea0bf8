#include <Arduino.h>
#include <Wire.h>                // For I2C communication
#include <WiFi.h>                // WiFi functionality
#include <WebServer.h>           // Web server functionality
#include <LittleFS.h>            // File system for web files
#include <Preferences.h>         // EEPROM storage
#include <ArduinoJson.h>         // JSON handling
#include <DFRobot_TCS3430.h>     // DFRobot TCS3430 Sensor Library
#include "HardwareConfig.h"      // Hardware pin definitions

// ------------------------------------------------------------------------------------
// Hardware Configuration for ESP32-S3 ProS3
// ------------------------------------------------------------------------------------
#define RGB_LED_PIN 18           // ESP32-S3 ProS3 onboard RGB LED
#define LDO2_PIN 17             // RGB LED power control
#define I2C_SDA_PIN 8           // TCS3430 sensor SDA
#define I2C_SCL_PIN 9           // TCS3430 sensor SCL
#define SENSOR_INTERRUPT_PIN 17  // TCS3430 interrupt pin

// WiFi Configuration
const char* ssid = "Wifi 6";
const char* password = "Scrofani1985";

// ------------------------------------------------------------------------------------
// Color Data Structures
// ------------------------------------------------------------------------------------
struct PrecisionColorData {
    float r, g, b;              // RGB values (0-255)
    float x, y, z;              // Raw XYZ tristimulus values
    float l, a, b_lab;          // Lab color space values
    float ir1, ir2;             // Infrared compensation values
    float confidence;           // Measurement confidence (0-100%)
    bool isValid;               // Data validity flag
};

struct ColorSample {
    // Color information
    uint8_t r, g, b;            // RGB values
    float rawX, rawY, rawZ;     // Raw sensor XYZ values
    float confidence;           // Measurement confidence
    char hexColor[8];           // Hex color string

    // Dulux paint match information
    char duluxName[64];         // Paint name
    char duluxCode[16];         // Paint code for purchasing
    char duluxID[16];           // Dulux internal ID
    uint8_t duluxR, duluxG, duluxB; // Matched paint RGB
    float deltaE;               // Color difference (Delta E)
    uint16_t duluxLRV;          // Light Reflectance Value

    // Sample metadata
    unsigned long timestamp;    // When sample was captured
    int sampleNumber;           // Sequential sample number
    bool isValid;              // Sample slot is occupied
};

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
Preferences preferences;
DFRobot_TCS3430 tcs3430_sensor;

// Scanning system variables
bool isScanning = false;
bool sensorInitialized = false;
bool ledState = false;

// Automatic scanning phases
enum ScanPhase {
    SCAN_IDLE,
    SCAN_PREPARATION,
    SCAN_ACTIVE,
    SCAN_PROCESSING
};

ScanPhase currentScanPhase = SCAN_IDLE;
unsigned long scanPhaseStartTime = 0;
unsigned long lastCountdownUpdate = 0;
int preparationCountdown = 3;
int scanningCountdown = 5;

// Sample collection for statistical averaging
#define MAX_SCAN_SAMPLES 25
PrecisionColorData scanSamples[MAX_SCAN_SAMPLES];
int currentScanSampleIndex = 0;
int totalValidSamples = 0;
unsigned long lastSampleTime = 0;

// Current color data
PrecisionColorData currentColor = {0};

// Sample storage (30-sample circular buffer)
#define MAX_STORED_SAMPLES 30
ColorSample storedSamples[MAX_STORED_SAMPLES];
int currentSampleIndex = 0;    // Next slot to write to
int totalSamples = 0;          // Total samples stored (max 30)

// Current frozen sample (displayed on right side)
ColorSample frozenSample = {0};
bool hasFrozenSample = false;

// ------------------------------------------------------------------------------------
// RGB LED Control Functions (ESP32-S3 ProS3)
// ------------------------------------------------------------------------------------
void initRGBLED() {
    pinMode(LDO2_PIN, OUTPUT);
    digitalWrite(LDO2_PIN, HIGH); // Enable RGB LED power
    pinMode(RGB_LED_PIN, OUTPUT);
    Serial.println("RGB LED initialized");
}

void setRGBColor(uint8_t r, uint8_t g, uint8_t b) {
    // Simple RGB control - for full NeoPixel support, use FastLED or Adafruit_NeoPixel
    // This is a basic implementation
    analogWrite(RGB_LED_PIN, (r + g + b) / 3); // Average for basic brightness control
}

void rainbowCycle() {
    static uint8_t colorIndex = 0;
    static unsigned long lastUpdate = 0;

    if (millis() - lastUpdate > 50) { // Update every 50ms
        uint8_t r = (sin(colorIndex * 0.1) + 1) * 127;
        uint8_t g = (sin(colorIndex * 0.1 + 2.094) + 1) * 127;
        uint8_t b = (sin(colorIndex * 0.1 + 4.188) + 1) * 127;

        setRGBColor(r, g, b);
        colorIndex++;
        lastUpdate = millis();
    }
}

// ------------------------------------------------------------------------------------
// Sensor Interrupt Handling
// ------------------------------------------------------------------------------------
volatile bool color_change_interrupt_flag = false;

void IRAM_ATTR handle_sensor_interrupt() {
    color_change_interrupt_flag = true; // Set flag, keep ISR short
}

// ------------------------------------------------------------------------------------
// Sample Storage Management Functions
// ------------------------------------------------------------------------------------
void saveSamplesToEEPROM() {
    preferences.begin("colorSamples", false);

    // Save sample metadata
    preferences.putInt("totalSamples", totalSamples);
    preferences.putInt("currentIndex", currentSampleIndex);

    // Save each sample
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        String key = "sample_" + String(i);
        if (storedSamples[i].isValid) {
            preferences.putBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
        } else {
            // Only try to remove if the key exists to avoid "NOT_FOUND" errors
            if (preferences.getBytesLength(key.c_str()) > 0) {
                preferences.remove(key.c_str());
            }
        }
    }

    preferences.end();
    Serial.printf("Saved %d samples to EEPROM\n", totalSamples);
}

void loadSamplesFromEEPROM() {
    preferences.begin("colorSamples", true);

    // Load sample metadata
    totalSamples = preferences.getInt("totalSamples", 0);
    currentSampleIndex = preferences.getInt("currentIndex", 0);

    // Initialize all samples as invalid
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }

    // Load each sample
    int loadedCount = 0;
    int slotsToCheck = min(totalSamples + 5, MAX_STORED_SAMPLES);

    for (int i = 0; i < slotsToCheck; i++) {
        String key = "sample_" + String(i);
        size_t dataSize = preferences.getBytesLength(key.c_str());
        if (dataSize == sizeof(ColorSample)) {
            size_t bytesRead = preferences.getBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
            if (bytesRead == sizeof(ColorSample) && storedSamples[i].isValid) {
                loadedCount++;
            }
        }
    }

    preferences.end();
    Serial.printf("Loaded %d samples from EEPROM\n", loadedCount);
}

void addSampleToStorage(const ColorSample& sample) {
    // Add sample to circular buffer
    storedSamples[currentSampleIndex] = sample;
    storedSamples[currentSampleIndex].isValid = true;
    storedSamples[currentSampleIndex].timestamp = millis();

    // Update indices
    currentSampleIndex = (currentSampleIndex + 1) % MAX_STORED_SAMPLES;
    if (totalSamples < MAX_STORED_SAMPLES) {
        totalSamples++;
    }

    // Save to EEPROM
    saveSamplesToEEPROM();

    Serial.printf("Sample added to storage. Total samples: %d\n", totalSamples);
}

// ------------------------------------------------------------------------------------
// Color Sensor Reading Functions
// ------------------------------------------------------------------------------------
bool readPrecisionColorSensor() {
    if (!sensorInitialized) return false;

    // Read raw data from TCS3430 sensor
    uint16_t x_val_raw = tcs3430_sensor.getXData();
    uint16_t y_val_raw = tcs3430_sensor.getYData();
    uint16_t z_val_raw = tcs3430_sensor.getZData();
    uint16_t ir1_val_raw = tcs3430_sensor.getIR1Data();
    uint16_t ir2_val_raw = tcs3430_sensor.getIR2Data();

    // Store raw sensor values
    currentColor.x = (float)x_val_raw;
    currentColor.y = (float)y_val_raw;
    currentColor.z = (float)z_val_raw;
    currentColor.ir1 = (float)ir1_val_raw;
    currentColor.ir2 = (float)ir2_val_raw;

    // Basic XYZ to RGB conversion (simplified)
    // In a full implementation, you would apply proper color space conversion
    float normFactor = 20000.0f; // Normalization factor
    currentColor.r = constrain((currentColor.x / normFactor) * 255, 0, 255);
    currentColor.g = constrain((currentColor.y / normFactor) * 255, 0, 255);
    currentColor.b = constrain((currentColor.z / normFactor) * 255, 0, 255);

    // Calculate confidence based on signal strength
    float totalSignal = currentColor.x + currentColor.y + currentColor.z;
    currentColor.confidence = constrain((totalSignal / 30000.0f) * 100, 0, 100);

    currentColor.isValid = true;
    return true;
}

void initializeScanSamples() {
    currentScanSampleIndex = 0;
    totalValidSamples = 0;
    for (int i = 0; i < MAX_SCAN_SAMPLES; i++) {
        scanSamples[i].isValid = false;
    }
}

void addScanSample(const PrecisionColorData& sample) {
    if (currentScanSampleIndex < MAX_SCAN_SAMPLES) {
        scanSamples[currentScanSampleIndex] = sample;
        scanSamples[currentScanSampleIndex].isValid = true;
        currentScanSampleIndex++;
        totalValidSamples++;
        Serial.printf("Added scan sample %d/%d\n", currentScanSampleIndex, MAX_SCAN_SAMPLES);
    }
}

PrecisionColorData calculateStatisticalAverage() {
    PrecisionColorData averaged = {0};

    if (totalValidSamples == 0) {
        Serial.println("No valid samples for averaging");
        return averaged;
    }

    // Calculate averages
    float sumR = 0, sumG = 0, sumB = 0;
    float sumX = 0, sumY = 0, sumZ = 0;
    float sumConfidence = 0;
    int validCount = 0;

    for (int i = 0; i < totalValidSamples; i++) {
        if (scanSamples[i].isValid) {
            sumR += scanSamples[i].r;
            sumG += scanSamples[i].g;
            sumB += scanSamples[i].b;
            sumX += scanSamples[i].x;
            sumY += scanSamples[i].y;
            sumZ += scanSamples[i].z;
            sumConfidence += scanSamples[i].confidence;
            validCount++;
        }
    }

    if (validCount > 0) {
        averaged.r = sumR / validCount;
        averaged.g = sumG / validCount;
        averaged.b = sumB / validCount;
        averaged.x = sumX / validCount;
        averaged.y = sumY / validCount;
        averaged.z = sumZ / validCount;
        averaged.confidence = sumConfidence / validCount;
        averaged.isValid = true;

        Serial.printf("Statistical average: RGB(%.1f,%.1f,%.1f) Confidence: %.1f%%\n",
                     averaged.r, averaged.g, averaged.b, averaged.confidence);
    }

    return averaged;
}

// ------------------------------------------------------------------------------------
// Setup Function
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    Serial.println("ESP32-S3 Color Measurement Device Starting...");

    // Initialize RGB LED
    initRGBLED();
    setRGBColor(50, 0, 50); // Purple boot indicator

    // Initialize I2C communication
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Serial.println("I2C interface initialized.");

    // Initialize LittleFS for web files
    if (!LittleFS.begin(true)) {
        Serial.println("ERROR: LittleFS Mount Failed");
        return;
    }
    Serial.println("LittleFS mounted successfully");

    // Load saved samples from EEPROM
    loadSamplesFromEEPROM();

    // Initialize WiFi
    WiFi.begin(ssid, password);
    Serial.print("Connecting to WiFi");

    int wifiAttempts = 0;
    while (WiFi.status() != WL_CONNECTED && wifiAttempts < 20) {
        delay(500);
        Serial.print(".");
        wifiAttempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.print("WiFi connected! IP address: ");
        Serial.println(WiFi.localIP());
        setRGBColor(0, 50, 0); // Green for WiFi success
    } else {
        Serial.println();
        Serial.println("WiFi connection failed! Starting in AP mode...");
        WiFi.softAP("ESP32-ColorMatcher", "colormatching");
        Serial.print("AP IP address: ");
        Serial.println(WiFi.softAPIP());
        setRGBColor(50, 25, 0); // Orange for AP mode
    }

    // Initialize TCS3430 Color Sensor
    Serial.println("Attempting to initialize TCS3430 color sensor...");
    int sensor_retry_count = 0;
    const int MAX_SENSOR_RETRIES = 3;

    while (!tcs3430_sensor.begin() && sensor_retry_count < MAX_SENSOR_RETRIES) {
        sensor_retry_count++;
        Serial.printf("TCS3430 sensor not detected (attempt %d/%d). Check I2C connection.\n", sensor_retry_count, MAX_SENSOR_RETRIES);
        delay(1000);
    }

    if (sensor_retry_count >= MAX_SENSOR_RETRIES) {
        Serial.println("WARNING: TCS3430 sensor not found. Application will continue without sensor.");
        sensorInitialized = false;
        setRGBColor(50, 0, 0); // Red for sensor error
    } else {
        Serial.println("TCS3430 color sensor detected and initialized.");
        sensorInitialized = true;

        // Configure sensor parameters
        tcs3430_sensor.setIntegrationTime(0x40); // ~181ms integration time
        tcs3430_sensor.setALSGain(1);            // 4x gain
        tcs3430_sensor.setALSInterrupt(true);    // Enable ALS interrupt
        tcs3430_sensor.setInterruptPersistence(0x05);
        tcs3430_sensor.setCH0IntThreshold(1000, 50000);
        tcs3430_sensor.setIntReadClear(true);
        tcs3430_sensor.setSleepAfterInterrupt(true);

        setRGBColor(0, 0, 50); // Blue for sensor ready
    }

    // Setup sensor interrupt pin
    if (SENSOR_INTERRUPT_PIN >= 0) {
        pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
        attachInterrupt(digitalPinToInterrupt(SENSOR_INTERRUPT_PIN), handle_sensor_interrupt, FALLING);
        Serial.println("Interrupt configured.");
    }

    // Initialize scanning system
    initializeScanSamples();

    Serial.println("Setup complete. Starting web server...");
    setRGBColor(0, 0, 0); // Turn off indicator LED
    // Setup web server routes
    setupWebServer();

    Serial.println("ESP32-S3 Color Measurement Device Ready!");
    Serial.print("Web interface: http://");
    Serial.println(WiFi.localIP());
}

// ------------------------------------------------------------------------------------
// Web Server Handler Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    if (LittleFS.exists("/index.html")) {
        server.sendHeader("Cache-Control", "no-cache");
        server.send_P(200, "text/html", (const char*)LittleFS.open("/index.html", "r").readString().c_str());
    } else {
        server.send(500, "text/plain", "index.html not found. Upload data folder first.");
    }
}

void handleStyleCSS() {
    if (LittleFS.exists("/style.css")) {
        server.sendHeader("Cache-Control", "max-age=86400");
        server.send_P(200, "text/css", (const char*)LittleFS.open("/style.css", "r").readString().c_str());
    } else {
        server.send(404, "text/plain", "style.css not found");
    }
}

void handleFullData() {
    DynamicJsonDocument doc(2048);

    // Current measurement data
    doc["data_ready"] = currentColor.isValid;
    doc["measured_r"] = (int)currentColor.r;
    doc["measured_g"] = (int)currentColor.g;
    doc["measured_b"] = (int)currentColor.b;
    doc["raw_x"] = currentColor.x;
    doc["raw_y"] = currentColor.y;
    doc["raw_z"] = currentColor.z;
    doc["ir1"] = currentColor.ir1;
    doc["ir2"] = currentColor.ir2;
    doc["confidence"] = currentColor.confidence;

    // Hex color
    char hexColor[8];
    sprintf(hexColor, "#%02X%02X%02X", (int)currentColor.r, (int)currentColor.g, (int)currentColor.b);
    doc["hex_color"] = hexColor;

    // Scanning status
    doc["is_scanning"] = isScanning;
    doc["scan_phase"] = (int)currentScanPhase;

    // LED state
    doc["led_state"] = ledState;

    // Sample count
    doc["total_samples"] = totalSamples;
    doc["total_stored_samples"] = totalSamples;

    // Frozen sample data
    if (hasFrozenSample) {
        doc["frozen_sample"] = true;
        doc["frozen_r"] = frozenSample.r;
        doc["frozen_g"] = frozenSample.g;
        doc["frozen_b"] = frozenSample.b;
        doc["frozen_hex"] = frozenSample.hexColor;
        doc["frozen_confidence"] = frozenSample.confidence;
    } else {
        doc["frozen_sample"] = false;
    }

    // Matched color (placeholder - would need Dulux database)
    doc["matched_name"] = "Scanning...";
    doc["matched_r"] = (int)currentColor.r;
    doc["matched_g"] = (int)currentColor.g;
    doc["matched_b"] = (int)currentColor.b;

    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
}

void handleStartScan() {
    if (currentScanPhase == SCAN_IDLE) {
        // Start the automatic 7-second scanning sequence
        currentScanPhase = SCAN_PREPARATION;
        scanPhaseStartTime = millis();
        lastCountdownUpdate = millis();
        preparationCountdown = 3;
        scanningCountdown = 5;

        // Initialize sample collection
        initializeScanSamples();

        isScanning = true;
        Serial.println("=== AUTOMATIC SCAN SEQUENCE STARTED ===");
        Serial.println("Phase 1: Preparation countdown (3 seconds)");
        server.send(200, "text/plain", "Auto scan started - 7 second sequence");
    } else {
        server.send(400, "text/plain", "Scan already in progress");
    }
}

void handleStopScan() {
    if (currentScanPhase != SCAN_IDLE) {
        Serial.println("=== AUTOMATIC SCAN INTERRUPTED ===");
        currentScanPhase = SCAN_IDLE;
        isScanning = false;
        server.send(200, "text/plain", "Automatic scan interrupted");
    } else {
        server.send(200, "text/plain", "No scan in progress");
    }
}

void handleToggleLED() {
    ledState = !ledState;
    if (ledState) {
        setRGBColor(255, 255, 255); // White LED on
    } else {
        setRGBColor(0, 0, 0); // LED off
    }

    DynamicJsonDocument doc(128);
    doc["led_state"] = ledState;
    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
}

void handleGetSamples() {
    DynamicJsonDocument doc(8192);
    doc["total_samples"] = totalSamples;

    JsonArray samplesArray = doc.createNestedArray("samples");

    // Add samples to JSON array
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        if (storedSamples[i].isValid) {
            JsonObject sample = samplesArray.createNestedObject();
            sample["sample_number"] = storedSamples[i].sampleNumber;
            sample["r"] = storedSamples[i].r;
            sample["g"] = storedSamples[i].g;
            sample["b"] = storedSamples[i].b;
            sample["hex_color"] = storedSamples[i].hexColor;
            sample["confidence"] = storedSamples[i].confidence;
            sample["raw_x"] = storedSamples[i].rawX;
            sample["raw_y"] = storedSamples[i].rawY;
            sample["raw_z"] = storedSamples[i].rawZ;
            sample["dulux_name"] = storedSamples[i].duluxName;
            sample["dulux_code"] = storedSamples[i].duluxCode;
            sample["dulux_id"] = storedSamples[i].duluxID;
            sample["dulux_r"] = storedSamples[i].duluxR;
            sample["dulux_g"] = storedSamples[i].duluxG;
            sample["dulux_b"] = storedSamples[i].duluxB;
            sample["dulux_lrv"] = storedSamples[i].duluxLRV;
            sample["delta_e"] = storedSamples[i].deltaE;
            sample["timestamp"] = storedSamples[i].timestamp;
        }
    }

    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
}

void handleClearSamples() {
    // Clear all samples
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }
    totalSamples = 0;
    currentSampleIndex = 0;
    hasFrozenSample = false;

    // Save to EEPROM
    saveSamplesToEEPROM();

    Serial.println("All samples cleared");
    server.send(200, "text/plain", "All samples cleared");
}

void handleWhiteBalance() {
    // Placeholder for white balance calibration
    server.send(200, "text/plain", "White balance calibration completed");
}

void setupWebServer() {
    // Basic routes
    server.on("/", handleRoot);
    server.on("/style.css", handleStyleCSS);
    server.on("/fulldata", handleFullData);

    // Scanning control
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);

    // Sample management
    server.on("/get_samples", HTTP_GET, handleGetSamples);
    server.on("/clear_samples", HTTP_POST, handleClearSamples);

    // Calibration
    server.on("/white_balance", HTTP_POST, handleWhiteBalance);

    // Start server
    server.begin();
    Serial.println("Web server started");
}

void processAndSaveStatisticalResult() {
    Serial.println("Processing statistical average from collected samples...");

    // Calculate statistical average with outlier filtering
    PrecisionColorData averagedColor = calculateStatisticalAverage();

    if (averagedColor.r == 0 && averagedColor.g == 0 && averagedColor.b == 0) {
        Serial.println("ERROR: No valid samples collected during scan");
        return;
    }

    // Create new sample for storage
    ColorSample newSample = {0};
    newSample.r = (uint8_t)averagedColor.r;
    newSample.g = (uint8_t)averagedColor.g;
    newSample.b = (uint8_t)averagedColor.b;
    newSample.rawX = averagedColor.x;
    newSample.rawY = averagedColor.y;
    newSample.rawZ = averagedColor.z;
    newSample.confidence = averagedColor.confidence;

    // Generate hex color
    sprintf(newSample.hexColor, "#%02X%02X%02X", newSample.r, newSample.g, newSample.b);

    // Placeholder Dulux matching (would need actual database)
    strcpy(newSample.duluxName, "Color Match Pending");
    strcpy(newSample.duluxCode, "N/A");
    strcpy(newSample.duluxID, "N/A");
    newSample.duluxR = newSample.r;
    newSample.duluxG = newSample.g;
    newSample.duluxB = newSample.b;
    newSample.deltaE = 0.0f;
    newSample.duluxLRV = 50; // Default LRV

    // Add sample metadata
    newSample.sampleNumber = totalSamples + 1;
    newSample.timestamp = millis();
    newSample.isValid = true;

    // Add to storage
    addSampleToStorage(newSample);

    // Update frozen sample
    frozenSample = newSample;
    hasFrozenSample = true;

    Serial.printf("Sample saved: RGB(%d,%d,%d) Confidence: %.1f%%\n",
                 newSample.r, newSample.g, newSample.b, newSample.confidence);
}

// ------------------------------------------------------------------------------------
// Main Loop
// ------------------------------------------------------------------------------------
void loop() {
    unsigned long currentTime = millis();

    // Handle web server requests
    server.handleClient();

    // Handle sensor interrupt
    if (color_change_interrupt_flag) {
        color_change_interrupt_flag = false;
        if (sensorInitialized) {
            tcs3430_sensor.getDeviceStatus(); // Clear interrupt status
        }
        Serial.println("Color change interrupt detected");
    }

    // Handle automatic scanning phases
    handleAutomaticScanning(currentTime);

    // Continuous sensor reading when not in automatic scan mode
    if (currentScanPhase == SCAN_IDLE && sensorInitialized) {
        static unsigned long lastSensorRead = 0;
        if (currentTime - lastSensorRead > 500) { // Read every 500ms
            readPrecisionColorSensor();
            lastSensorRead = currentTime;

            // Update RGB LED with measured color if LED is on
            if (ledState && !isScanning) {
                setRGBColor((uint8_t)(currentColor.r * 0.3),
                           (uint8_t)(currentColor.g * 0.3),
                           (uint8_t)(currentColor.b * 0.3));
            }
        }
    }

    // Rainbow cycling when idle and LED is off
    if (!ledState && !isScanning && currentScanPhase == SCAN_IDLE) {
        rainbowCycle();
    }

    delay(10); // Small delay for stability
}

void handleAutomaticScanning(unsigned long currentTime) {
    unsigned long phaseTime = currentTime - scanPhaseStartTime;

    switch (currentScanPhase) {
        case SCAN_PREPARATION:
            // Phase 1: 3-second preparation countdown
            if (currentTime - lastCountdownUpdate >= 1000) {
                preparationCountdown--;
                lastCountdownUpdate = currentTime;
                Serial.printf("Place object near sensor... %d\n", preparationCountdown);

                // Flash LED during preparation
                setRGBColor(50, 50, 0); // Yellow preparation indicator
            }

            if (phaseTime >= 3000) {
                // Move to active scanning phase
                currentScanPhase = SCAN_ACTIVE;
                scanPhaseStartTime = currentTime;
                lastSampleTime = currentTime;
                Serial.println("Phase 2: Active scanning (5 seconds)");
            }
            break;

        case SCAN_ACTIVE:
            // Phase 2: 5-second active scanning with sample collection

            // Update countdown display
            if (currentTime - lastCountdownUpdate >= 1000) {
                scanningCountdown--;
                lastCountdownUpdate = currentTime;
                Serial.printf("Scanning... %d seconds remaining\n", scanningCountdown);
            }

            // Collect samples every 200ms (25 samples over 5 seconds)
            if (sensorInitialized && currentTime - lastSampleTime >= 200) {
                lastSampleTime = currentTime;

                if (readPrecisionColorSensor()) {
                    // Add sample to collection for statistical processing
                    addScanSample(currentColor);

                    // Show live color on LED during scanning
                    setRGBColor((uint8_t)currentColor.r,
                               (uint8_t)currentColor.g,
                               (uint8_t)currentColor.b);
                }
            }

            if (phaseTime >= 5000) {
                // Move to processing phase
                currentScanPhase = SCAN_PROCESSING;
                scanPhaseStartTime = currentTime;
                Serial.println("Phase 3: Processing and saving...");
            }
            break;

        case SCAN_PROCESSING:
            // Phase 3: Process collected samples and save result
            if (phaseTime >= 500) { // Brief processing delay for visual feedback
                processAndSaveStatisticalResult();

                // Return to idle state
                currentScanPhase = SCAN_IDLE;
                isScanning = false;
                Serial.println("=== AUTOMATIC SCAN SEQUENCE COMPLETED ===");

                // Flash green to indicate completion
                setRGBColor(0, 100, 0);
                delay(200);
                setRGBColor(0, 0, 0);
            }
            break;

        case SCAN_IDLE:
        default:
            // No automatic scanning in progress
            break;
    }
}