<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32-S3 Color Matcher</title>
    <link rel="stylesheet" href="style.css?v=2" />
</head>
<body>
    <div class="container">
        <h1>ESP32-S3 Color Matcher</h1>
         
        <div class="control-buttons">
            <button id="led-button" class="off">Toggle LED</button>
            <button id="scan-button">Auto Scan (7s)</button>
            <button id="white-balance-button">White Balance</button>
        </div>
        <p id="scan-status"></p>
        <div id="countdown-display" class="countdown-display" style="display: none;"></div>

        <div class="card">
            <div class="color-display">
                <div class="color-card">
                    <h2>Measured Color</h2>
                    <div id="measured-swatch" class="swatch"></div>
                    <div class="details">
                        <p id="measured-rgb">RGB: (..., ..., ...)</p>
                        <p id="measured-hex">Hex: #------</p>
                    </div>
                </div>
                <div class="color-card">
                    <h2>Closest Dulux Match</h2>
                    <div id="matched-swatch" class="swatch"></div>
                    <div class="details">
                        <p id="matched-name">Scanning...</p>
                        <p id="matched-rgb">RGB: (..., ..., ...)</p>
                    </div>
                </div>
            </div>
            <div class="stats">
                <div class="capture-display">
                    <h3>Captured Sample</h3>
                    <div id="captured-swatch" class="swatch"></div>
                    <div class="capture-details">
                        <p id="captured-rgb">RGB: (Press scan to capture)</p>
                        <p id="captured-hex">Hex: #------</p>
                        <p id="capture-status">Ready to capture</p>
                    </div>
                </div>
                <div class="sample-info">
                    <p id="sample-count">Saved Samples: 0/30</p>
                    <button id="save-sample-button" class="save-button" style="display: none;">💾 Save Sample</button>
                    <button id="view-samples-button" class="samples-button">📁 View Saved Samples</button>
                    <div id="save-notification" class="save-notification" style="display: none;">
                        <span class="save-icon">✓</span>
                        <span class="save-text">Sample Saved!</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="collapsible-header">Advanced Details</div>
            <div class="collapsible-content">
                <div class="live-data-grid">
                    <div><p>XYZ: <span id="live-xyz">--</span></p></div>
                    <div><p>Lab: <span id="live-lab">--</span></p></div>
                    <div><p>IR1/IR2: <span id="live-ir">--</span></p></div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="collapsible-header">Sensor Calibration</div>
            <div class="collapsible-content">
                <form id="calib-form">
                    <div class="form-grid">
                        <div>
                            <label for="calib-gain">ALS Gain</label>
                            <select id="calib-gain" name="gain">
                                <option value="1">1x</option><option value="4">4x</option>
                                <option value="16">16x</option><option value="64">64x</option>
                            </select>
                        </div>
                        <div>
                            <label for="calib-ir">IR Compensation Factor</label>
                            <input type="number" id="calib-ir" name="ir_comp" step="0.01" min="0" max="1" />
                        </div>
                        <div>
                            <label for="calib-norm">sRGB Normalization</label>
                            <input type="number" id="calib-norm" name="norm" step="1000" min="1000" max="100000" />
                        </div>
                    </div>
                    <div class="form-actions">
                        <p id="save-status"></p>
                        <button type="submit">Apply &amp; Save</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- New Advanced Settings Card -->
        <div class="card">
            <div class="collapsible-header">Advanced Settings</div>
            <div class="collapsible-content">
                <div class="tabs">
                    <div class="tab active" data-tab="sensor-settings">Sensor Settings</div>
                    <div class="tab" data-tab="color-preview">Live Preview</div>
                </div>
                 
                <div class="tab-content active" id="sensor-settings">
                    <form id="advanced-settings-form">
                        <div class="setting-group">
                            <h3>Sensor Configuration</h3>
                             
                            <div>
                                <label for="integration-time">Integration Time</label>
                                <select id="integration-time" name="integration_time">
                                    <option value="0x01">2.78ms (Fastest)</option>
                                    <option value="0x11">50ms (Balanced)</option>
                                    <option value="0x23">100ms (Accurate)</option>
                                    <option value="0x40">181ms (High Precision)</option>
                                    <option value="0xFF">711ms (Maximum)</option>
                                </select>
                                <div class="setting-description">
                                    Controls how long the sensor collects light. Longer times provide more accurate readings in low light but slower response.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Bright conditions: 2.78ms - 50ms</li>
                                        <li>Normal indoor lighting: 50ms - 100ms</li>
                                        <li>Low light conditions: 181ms - 711ms</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-gain">ALS Gain</label>
                                <select id="advanced-gain" name="gain">
                                    <option value="1">1x</option>
                                    <option value="4">4x</option>
                                    <option value="16">16x</option>
                                    <option value="64">64x</option>
                                </select>
                                <div class="setting-description">
                                    Amplifies the sensor signal. Higher gain improves sensitivity in low light but may cause saturation in bright conditions.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Direct sunlight: 1x</li>
                                        <li>Bright indoor lighting: 4x</li>
                                        <li>Normal indoor lighting: 16x</li>
                                        <li>Dim lighting: 64x</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                         
                        <div class="setting-group">
                            <h3>Color Processing</h3>
                             
                            <div>
                                <label for="advanced-ir-comp">IR Compensation Factor</label>
                                <input type="number" id="advanced-ir-comp" name="ir_comp" step="0.01" min="0" max="1" />
                                <div class="error-message" id="ir-comp-error">Value must be between 0 and 1</div>
                                <div class="setting-description">
                                    Reduces infrared light interference. Higher values remove more IR influence, improving color accuracy under artificial lighting.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Natural daylight: 0.01 - 0.03</li>
                                        <li>LED lighting: 0.02 - 0.05</li>
                                        <li>Fluorescent lighting: 0.03 - 0.07</li>
                                        <li>Incandescent lighting: 0.05 - 0.10</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-norm">sRGB Normalization Factor</label>
                                <input type="number" id="advanced-norm" name="norm" step="1000" min="1000" max="100000" />
                                <div class="error-message" id="norm-error">Value must be between 1,000 and 100,000</div>
                                <div class="setting-description">
                                    Scales raw sensor values to sRGB color space. Lower values make colors appear brighter, higher values make them darker.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Low light: 5,000 - 10,000</li>
                                        <li>Normal lighting: 10,000 - 20,000</li>
                                        <li>Bright lighting: 20,000 - 40,000</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="adaptive-scaling">Adaptive Scaling</label>
                                <select id="adaptive-scaling" name="adaptive_scaling">
                                    <option value="true">Enabled</option>
                                    <option value="false">Disabled</option>
                                </select>
                                <div class="setting-description">
                                    When enabled, automatically adjusts color scaling based on light intensity. Improves performance across varying lighting conditions.
                                </div>
                            </div>
                        </div>
                         
                        <div class="form-actions">
                            <p id="advanced-save-status"></p>
                            <button type="button" id="cancel-button">Cancel</button>
                            <button type="submit">Apply &amp; Save</button>
                        </div>
                    </form>
                </div>
                 
                <div class="tab-content" id="color-preview">
                    <h3>Live Settings Preview</h3>
                    <p>Adjust settings to see their effect on color detection in real-time.</p>
                     
                    <div class="preview-container">
                        <div class="preview-box">
                            <h4>Current Settings</h4>
                            <div id="current-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="current-rgb">--</span></p>
                                <p>XYZ: <span id="current-xyz">--</span></p>
                            </div>
                        </div>
                        <div class="preview-box">
                            <h4>New Settings</h4>
                            <div id="new-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="new-rgb">--</span></p>
                                <p>XYZ: <span id="new-xyz">--</span></p>
                            </div>
                        </div>
                    </div>
                     
                    <div class="nav-buttons">
                        <button type="button" id="back-to-settings">Back to Settings</button>
                        <button type="button" id="apply-preview">Apply These Settings</button>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            ESP32PROS3 &amp; TCS3430<br>
            <span class="footer-signature">Scrofani</span>
        </footer>
    </div>

    <!-- Saved Samples Modal -->
    <div id="samples-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Saved Color Samples</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="samples-grid" class="samples-grid">
                    <!-- Sample thumbnails will be loaded here -->
                </div>
                <div id="samples-loading" class="loading">Loading samples...</div>
                <div id="samples-empty" class="empty-state" style="display: none;">
                    <p>No saved samples yet.</p>
                    <p>Start scanning and stopping to save color samples!</p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="clear-all-samples" class="danger">Clear All Samples</button>
                <button id="close-samples-modal" class="secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- Sample Detail Modal -->
    <div id="sample-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sample Details</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="sample-detail-content">
                    <!-- Sample details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="delete-sample" class="danger">Delete Sample</button>
                <button id="close-detail-modal" class="secondary">Close</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const collapsibles = document.querySelectorAll('.collapsible-header');
            collapsibles.forEach(header => {
                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                    } else {
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            });

            const calibForm = document.getElementById('calib-form');
            calibForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const saveStatus = document.getElementById('save-status');
                saveStatus.textContent = 'Saving...';
                
                fetch('/set_calibration', {
                    method: 'POST',
                    body: new URLSearchParams(formData)
                }).then(response => {
                    if(response.ok) return response.text();
                    throw new Error('Save failed.');
                }).then(text => {
                    saveStatus.textContent = text;
                    setTimeout(() => saveStatus.textContent = '', 3000);
                }).catch(error => {
                    saveStatus.textContent = 'Error!';
                    setTimeout(() => saveStatus.textContent = '', 3000);
                });
            });

            const ledButton = document.getElementById('led-button');
            const scanButton = document.getElementById('scan-button');
            const scanStatus = document.getElementById('scan-status');
            const whiteBalanceButton = document.getElementById('white-balance-button');
            const viewSamplesButton = document.getElementById('view-samples-button');

            ledButton.addEventListener('click', () => {
                fetch('/toggle_led', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        updateLedButton(data.led_state);
                    })
                    .catch(error => console.error('Error toggling LED:', error));
            });

            let scanInProgress = false;
            let scanPhase = 'idle';

            scanButton.addEventListener('click', () => {
                if (!scanInProgress) {
                    startAutomaticScan();
                } else {
                    interruptScan();
                }
            });

            function startAutomaticScan() {
                scanInProgress = true;
                scanPhase = 'preparation';
                scanButton.textContent = 'Cancel Scan';
                scanButton.classList.add('stop');

                fetch('/start_scan', { method: 'POST' })
                    .then(response => response.text())
                    .then(text => {
                        console.log('Auto scan started:', text);
                        startPreparationPhase();
                    })
                    .catch(error => {
                        console.error('Error starting scan:', error);
                        resetScanUI();
                    });
            }

            function interruptScan() {
                fetch('/stop_scan', { method: 'POST' })
                    .then(response => response.text())
                    .then(text => {
                        console.log('Scan interrupted:', text);
                        resetScanUI();
                    })
                    .catch(error => {
                        console.error('Error interrupting scan:', error);
                        resetScanUI();
                    });
            }

            function startPreparationPhase() {
                scanPhase = 'preparation';
                let countdown = 3;
                const countdownDisplay = document.getElementById('countdown-display');

                countdownDisplay.className = 'countdown-display preparation';
                countdownDisplay.style.display = 'block';
                scanStatus.textContent = 'Place object near sensor...';

                function updatePreparationCountdown() {
                    countdownDisplay.textContent = `Place object near sensor... ${countdown}`;
                    countdown--;

                    if (countdown < 0) {
                        startScanningPhase();
                    } else {
                        setTimeout(updatePreparationCountdown, 1000);
                    }
                }

                updatePreparationCountdown();
            }

            function startScanningPhase() {
                scanPhase = 'scanning';
                let countdown = 5;
                const countdownDisplay = document.getElementById('countdown-display');

                countdownDisplay.className = 'countdown-display scanning';
                scanStatus.textContent = 'Scanning in progress...';

                function updateScanningCountdown() {
                    countdownDisplay.textContent = `Scanning... ${countdown} seconds remaining`;
                    countdown--;

                    if (countdown < 0) {
                        startProcessingPhase();
                    } else {
                        setTimeout(updateScanningCountdown, 1000);
                    }
                }

                updateScanningCountdown();
            }

            function startProcessingPhase() {
                scanPhase = 'processing';
                const countdownDisplay = document.getElementById('countdown-display');

                countdownDisplay.className = 'countdown-display processing';
                countdownDisplay.textContent = 'Processing and saving...';
                scanStatus.textContent = 'Processing statistical average...';

                // Wait for processing to complete (monitor via data updates)
                setTimeout(checkForCompletion, 1000);
            }

            function checkForCompletion() {
                // Check if scan is still in progress via data fetch
                fetch('/fulldata')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.is_scanning) {
                            // Scan completed
                            completeScan();
                            showSaveConfirmation();
                        } else {
                            // Still processing, check again
                            setTimeout(checkForCompletion, 500);
                        }
                    })
                    .catch(error => {
                        console.error('Error checking completion:', error);
                        resetScanUI();
                    });
            }

            function completeScan() {
                scanPhase = 'idle';
                scanInProgress = false;

                const countdownDisplay = document.getElementById('countdown-display');
                countdownDisplay.style.display = 'none';

                scanButton.textContent = 'Auto Scan (7s)';
                scanButton.classList.remove('stop');
                scanStatus.textContent = '';
            }

            function resetScanUI() {
                scanPhase = 'idle';
                scanInProgress = false;

                const countdownDisplay = document.getElementById('countdown-display');
                countdownDisplay.style.display = 'none';

                scanButton.textContent = 'Auto Scan (7s)';
                scanButton.classList.remove('stop');
                scanStatus.textContent = '';
            }

            whiteBalanceButton.addEventListener('click', () => {
                const originalText = whiteBalanceButton.textContent;
                whiteBalanceButton.textContent = 'Calibrating...';
                whiteBalanceButton.disabled = true;

                fetch('/white_balance', { method: 'POST' })
                    .then(response => response.text())
                    .then(text => {
                        scanStatus.textContent = text;
                        setTimeout(() => scanStatus.textContent = '', 3000);
                    })
                    .catch(error => {
                        scanStatus.textContent = 'White balance calibration failed!';
                        setTimeout(() => scanStatus.textContent = '', 3000);
                        console.error('Error during white balance calibration:', error);
                    })
                    .finally(() => {
                        whiteBalanceButton.textContent = originalText;
                        whiteBalanceButton.disabled = false;
                    });
            });

            // Saved Samples Modal functionality
            viewSamplesButton.addEventListener('click', () => {
                showSamplesModal();
            });

            // Modal close functionality
            document.querySelectorAll('.modal .close, #close-samples-modal, #close-detail-modal').forEach(closeBtn => {
                closeBtn.addEventListener('click', (e) => {
                    const modal = e.target.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            });

            // Close modal when clicking outside
            window.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });

            // Clear all samples
            document.getElementById('clear-all-samples').addEventListener('click', () => {
                if (confirm('Are you sure you want to delete all saved samples? This cannot be undone.')) {
                    fetch('/clear_samples', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => {
                            console.log('Samples cleared:', text);
                            document.getElementById('samples-modal').style.display = 'none';
                            // Refresh the sample count
                            fetchData();
                        })
                        .catch(error => console.error('Error clearing samples:', error));
                }
            });

            function showSaveConfirmation() {
                // Show immediate notification
                const notification = document.getElementById('save-notification');
                notification.style.display = 'block';

                // Get current sample count and details
                fetch('/fulldata')
                    .then(response => response.json())
                    .then(data => {
                        const sampleCount = data.total_samples || data.total_stored_samples || 0;

                        // **ENHANCED VISUAL CONFIRMATION** - Show detailed sample information
                        if (data.frozen_sample) {
                            // Update notification with detailed information
                            document.querySelector('.save-text').innerHTML = `
                                <strong>Sample #${sampleCount} Saved!</strong><br>
                                <small>RGB(${data.frozen_r || '--'}, ${data.frozen_g || '--'}, ${data.frozen_b || '--'})</small>
                            `;

                            // Show detailed confirmation message in status
                            scanStatus.innerHTML = `
                                ✓ <strong>Sample automatically saved!</strong><br>
                                <small>Total: ${sampleCount}/30 | Confidence: ${data.frozen_confidence ? data.frozen_confidence.toFixed(1) + '%' : 'N/A'}</small>
                            `;
                            scanStatus.style.color = '#2e7d32'; // Green color

                            // **ENHANCED COLOR FLASH** - Show the saved color more prominently
                            if (data.frozen_hex) {
                                // Flash the captured swatch
                                const capturedSwatch = document.getElementById('captured-swatch');
                                capturedSwatch.style.transform = 'scale(1.1)';
                                capturedSwatch.style.boxShadow = '0 0 20px rgba(46, 125, 50, 0.6)';
                                capturedSwatch.style.transition = 'all 0.3s ease';

                                // Brief body background flash
                                const originalBg = document.body.style.backgroundColor;
                                document.body.style.backgroundColor = data.frozen_hex;
                                document.body.style.transition = 'background-color 0.3s ease';

                                setTimeout(() => {
                                    document.body.style.backgroundColor = originalBg;
                                    capturedSwatch.style.transform = '';
                                    capturedSwatch.style.boxShadow = '';
                                    setTimeout(() => {
                                        document.body.style.transition = '';
                                        capturedSwatch.style.transition = '';
                                    }, 300);
                                }, 500);
                            }
                        } else {
                            // Fallback for basic confirmation
                            document.querySelector('.save-text').textContent = `Sample saved! Total: ${sampleCount}/30`;
                            scanStatus.textContent = `✓ Sample saved! Total: ${sampleCount}/30`;
                            scanStatus.style.color = '#2e7d32';
                        }

                        // Hide notification after 4 seconds (extended for more detail)
                        setTimeout(() => {
                            notification.style.display = 'none';
                            scanStatus.textContent = '';
                            scanStatus.style.color = '#3949ab'; // Reset to original color
                        }, 4000);
                    })
                    .catch(error => {
                        console.error('Error getting sample count:', error);
                        document.querySelector('.save-text').textContent = 'Sample saved!';
                        scanStatus.textContent = '✓ Sample saved!';
                        scanStatus.style.color = '#2e7d32';
                        setTimeout(() => {
                            notification.style.display = 'none';
                            scanStatus.textContent = '';
                            scanStatus.style.color = '#3949ab';
                        }, 3000);
                    });
            }

            function showSamplesModal() {
                const modal = document.getElementById('samples-modal');
                const grid = document.getElementById('samples-grid');
                const loading = document.getElementById('samples-loading');
                const empty = document.getElementById('samples-empty');

                modal.style.display = 'block';
                loading.style.display = 'block';
                grid.style.display = 'none';
                empty.style.display = 'none';

                fetch('/get_samples')
                    .then(response => response.json())
                    .then(data => {
                        loading.style.display = 'none';

                        // **ENHANCED DEBUGGING** for sample loading issues
                        console.log('Sample API Response:', data);
                        console.log('Total samples from API:', data.total_samples);
                        console.log('Samples array:', data.samples);
                        console.log('Samples array length:', data.samples ? data.samples.length : 'undefined');

                        if (data.samples && data.samples.length > 0) {
                            console.log('Rendering', data.samples.length, 'samples');
                            grid.style.display = 'grid';
                            renderSampleThumbnails(data.samples);
                        } else {
                            console.log('No samples to display - showing empty state');
                            console.log('Reason: samples array is', data.samples ? 'empty' : 'undefined');
                            empty.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading samples:', error);
                        loading.style.display = 'none';
                        empty.style.display = 'block';
                    });
            }

            function renderSampleThumbnails(samples) {
                const grid = document.getElementById('samples-grid');
                grid.innerHTML = '';

                console.log('renderSampleThumbnails called with', samples.length, 'samples');

                samples.forEach((sample, index) => {
                    console.log(`Rendering sample ${index}:`, sample);

                    const thumbnail = document.createElement('div');
                    thumbnail.className = 'sample-thumbnail';

                    // **ENHANCED SAMPLE THUMBNAILS** with better Dulux information
                    const matchQuality = sample.delta_e <= 5.0 ? '🟢' : sample.delta_e <= 10.0 ? '🟡' : sample.delta_e <= 20.0 ? '🟠' : '🔴';
                    const lrvInfo = sample.dulux_lrv ? ` (LRV: ${sample.dulux_lrv})` : '';

                    // **DEBUGGING** - Check for missing fields
                    if (!sample.r && sample.r !== 0) console.warn(`Sample ${index} missing 'r' field`);
                    if (!sample.g && sample.g !== 0) console.warn(`Sample ${index} missing 'g' field`);
                    if (!sample.b && sample.b !== 0) console.warn(`Sample ${index} missing 'b' field`);

                    thumbnail.innerHTML = `
                        <div class="sample-color" style="background-color: rgb(${sample.r || 0}, ${sample.g || 0}, ${sample.b || 0})"></div>
                        <div class="sample-info">
                            <div class="sample-number">#${sample.sample_number || (index + 1)} ${matchQuality}</div>
                            <div class="sample-rgb">RGB(${sample.r || 0}, ${sample.g || 0}, ${sample.b || 0})</div>
                            <div class="sample-match" title="${sample.dulux_name || 'No Match'}">${(sample.dulux_name || 'No Match').substring(0, 25)}${(sample.dulux_name || '').length > 25 ? '...' : ''}</div>
                            <div class="sample-code" title="Paint Code: ${sample.dulux_code || 'N/A'}">${sample.dulux_code || 'No Code'}${lrvInfo}</div>
                            <div class="sample-confidence">ΔE: ${sample.delta_e ? sample.delta_e.toFixed(1) : 'N/A'} | ${sample.confidence ? sample.confidence.toFixed(0) + '%' : 'N/A'}</div>
                        </div>
                    `;

                    thumbnail.addEventListener('click', () => {
                        showSampleDetail(sample, index);
                    });

                    grid.appendChild(thumbnail);
                    console.log(`Sample ${index} thumbnail added to grid`);
                });

                console.log('All sample thumbnails rendered. Grid children count:', grid.children.length);
            }

            function showSampleDetail(sample, index) {
                const modal = document.getElementById('sample-detail-modal');
                const content = document.getElementById('sample-detail-content');

                content.innerHTML = `
                    <div class="sample-detail-grid">
                        <div class="detail-color-section">
                            <h3>Color Information</h3>
                            <div class="detail-color-swatch" style="background-color: rgb(${sample.r}, ${sample.g}, ${sample.b})"></div>
                            <div class="detail-color-info">
                                <p><strong>RGB:</strong> (${sample.r}, ${sample.g}, ${sample.b})</p>
                                <p><strong>Hex:</strong> ${sample.hex_color || '#' + ((1 << 24) + (sample.r << 16) + (sample.g << 8) + sample.b).toString(16).slice(1)}</p>
                                <p><strong>Confidence:</strong> ${sample.confidence ? sample.confidence.toFixed(1) + '%' : 'N/A'}</p>
                            </div>
                        </div>

                        <div class="detail-match-section">
                            <h3>🎨 Dulux Paint Match - For House Painting</h3>
                            <div class="detail-match-swatch" style="background-color: rgb(${sample.dulux_r}, ${sample.dulux_g}, ${sample.dulux_b})"></div>
                            <div class="detail-match-info">
                                <div class="paint-purchase-info">
                                    <h4>📋 Paint Purchase Information</h4>
                                    <p><strong>Paint Name:</strong> <span class="highlight">${sample.dulux_name || 'No Match Found'}</span></p>
                                    <p><strong>Paint Code:</strong> <span class="highlight">${sample.dulux_code || 'N/A'}</span></p>
                                    <p><strong>Dulux ID:</strong> <span class="highlight">${sample.dulux_id || 'N/A'}</span></p>
                                    <p><strong>LRV:</strong> ${sample.dulux_lrv || 'N/A'} <small>(Light Reflectance Value - brightness level)</small></p>
                                </div>

                                <div class="match-quality-info">
                                    <h4>🎯 Color Match Quality</h4>
                                    <p><strong>Delta E:</strong> ${sample.delta_e ? sample.delta_e.toFixed(1) : 'N/A'}</p>
                                    <p><strong>Match Quality:</strong>
                                        <span class="match-quality ${sample.delta_e <= 5.0 ? 'excellent' : sample.delta_e <= 10.0 ? 'good' : sample.delta_e <= 20.0 ? 'fair' : 'poor'}">
                                            ${sample.delta_e <= 5.0 ? '🟢 Excellent' : sample.delta_e <= 10.0 ? '🟡 Good' : sample.delta_e <= 20.0 ? '🟠 Fair' : '🔴 Poor'}
                                        </span>
                                    </p>
                                    <p><strong>Paint RGB:</strong> (${sample.dulux_r}, ${sample.dulux_g}, ${sample.dulux_b})</p>
                                </div>

                                <div class="purchase-notes">
                                    <h4>💡 Purchase Notes</h4>
                                    <ul>
                                        <li>Take this paint code to any Dulux retailer</li>
                                        <li>LRV indicates how light/dark the color appears</li>
                                        <li>Higher LRV = lighter color, Lower LRV = darker color</li>
                                        <li>Consider lighting conditions in your room</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="detail-sensor-section">
                            <h3>Raw Sensor Data</h3>
                            <div class="detail-sensor-info">
                                <p><strong>Sample Number:</strong> #${sample.sample_number || 'N/A'}</p>
                                <p><strong>XYZ Tristimulus:</strong> X=${sample.raw_x || 'N/A'}, Y=${sample.raw_y || 'N/A'}, Z=${sample.raw_z || 'N/A'}</p>
                                <p><strong>Confidence:</strong> ${sample.confidence ? sample.confidence.toFixed(1) + '%' : 'N/A'}</p>
                                <p><strong>Timestamp:</strong> ${new Date(sample.timestamp).toLocaleString()}</p>
                            </div>
                        </div>
                    </div>
                `;

                // Set up delete button
                const deleteBtn = document.getElementById('delete-sample');
                deleteBtn.onclick = () => {
                    if (confirm('Are you sure you want to delete this sample?')) {
                        fetch('/delete_sample', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: `index=${index}`
                        })
                        .then(response => response.text())
                        .then(text => {
                            console.log('Sample deleted:', text);
                            modal.style.display = 'none';
                            // Refresh the samples modal
                            showSamplesModal();
                        })
                        .catch(error => console.error('Error deleting sample:', error));
                    }
                };

                modal.style.display = 'block';
            }

            function updateLedButton(state) {
                ledButton.textContent = `LED: ${state ? 'ON' : 'OFF'}`;
                ledButton.className = state ? 'on' : 'off';
            }

            let isFirstFetch = true;
            function updateUI(data) {
                if (!data) return;

                if(data.data_ready){
                    // Update measured color (left side - live readings)
                    document.getElementById('measured-swatch').style.backgroundColor = `rgb(${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                    document.getElementById('measured-rgb').innerText = `RGB: (${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;

                    // Add hex color display if element exists
                    const hexElem = document.getElementById('measured-hex');
                    if (hexElem && data.hex_color) {
                        hexElem.innerText = `Hex: ${data.hex_color}`;
                    }

                    // Update matched color (right side - Dulux match)
                    document.getElementById('matched-swatch').style.backgroundColor = `rgb(${data.matched_r}, ${data.matched_g}, ${data.matched_b})`;
                    document.getElementById('matched-name').innerText = data.matched_name || 'Scanning...';
                    document.getElementById('matched-rgb').innerText = `RGB: (${data.matched_r}, ${data.matched_g}, ${data.matched_b})`;

                    // Update captured sample display
                    if (data.frozen_sample && data.frozen_hex) {
                        // Show frozen captured sample
                        document.getElementById('captured-swatch').style.backgroundColor = data.frozen_hex;
                        document.getElementById('captured-rgb').innerText = `RGB: (${data.frozen_r || '--'}, ${data.frozen_g || '--'}, ${data.frozen_b || '--'})`;
                        document.getElementById('captured-hex').innerText = `Hex: ${data.frozen_hex}`;
                        document.getElementById('capture-status').innerText = `Captured! Confidence: ${data.frozen_confidence ? data.frozen_confidence.toFixed(1) + '%' : 'N/A'}`;
                    } else if (data.is_scanning) {
                        // Show live preview during scanning
                        document.getElementById('captured-swatch').style.backgroundColor = `rgb(${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                        document.getElementById('captured-rgb').innerText = `RGB: (${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                        document.getElementById('captured-hex').innerText = `Hex: ${data.hex_color}`;
                        document.getElementById('capture-status').innerText = 'Live preview - scanning...';
                    } else {
                        // Show ready state
                        document.getElementById('captured-swatch').style.backgroundColor = '#f0f0f0';
                        document.getElementById('captured-rgb').innerText = 'RGB: (Press scan to capture)';
                        document.getElementById('captured-hex').innerText = 'Hex: #------';
                        document.getElementById('capture-status').innerText = 'Ready to capture';
                    }

                    // Update sample count with visual feedback
                    const sampleCount = data.total_samples || data.total_stored_samples || 0;
                    const sampleCountElement = document.getElementById('sample-count');
                    const oldCount = sampleCountElement.innerText.match(/(\d+)\/30/);
                    const oldNumber = oldCount ? parseInt(oldCount[1]) : 0;

                    sampleCountElement.innerText = `Saved Samples: ${sampleCount}/30`;

                    // Highlight if count increased
                    if (sampleCount > oldNumber) {
                        sampleCountElement.style.color = '#2e7d32';
                        sampleCountElement.style.fontWeight = 'bold';
                        setTimeout(() => {
                            sampleCountElement.style.color = '';
                            sampleCountElement.style.fontWeight = '';
                        }, 2000);
                    }

                    // Update advanced details if elements exist
                    const xyzElem = document.getElementById('live-xyz');
                    const labElem = document.getElementById('live-lab');
                    const irElem = document.getElementById('live-ir');

                    if (xyzElem && data.raw_x !== undefined) {
                        xyzElem.innerText = `${data.raw_x}, ${data.raw_y}, ${data.raw_z}`;
                    } else if (xyzElem && data.avg_x !== undefined) {
                        xyzElem.innerText = `${data.avg_x.toFixed(1)}, ${data.avg_y.toFixed(1)}, ${data.avg_z.toFixed(1)}`;
                    }

                    if (labElem && data.avg_l !== undefined) {
                        labElem.innerText = `${data.avg_l.toFixed(1)}, ${data.avg_a.toFixed(1)}, ${data.avg_b.toFixed(1)}`;
                    }

                    if (irElem && data.ir1 !== undefined) {
                        irElem.innerText = `${data.ir1} / ${data.ir2}`;
                    } else if (irElem && data.avg_ir1 !== undefined) {
                        irElem.innerText = `${data.avg_ir1.toFixed(1)} / ${data.avg_ir2.toFixed(1)}`;
                    }

                    // Update scan status for automatic scanning
                    if (!data.is_scanning && scanInProgress && scanPhase !== 'idle') {
                        // Automatic scan completed
                        completeScan();
                        showSaveConfirmation();
                    }
                    updateLedButton(data.led_state);
                }
                
                if (isFirstFetch) {
                    document.getElementById('calib-gain').value = data.calib_gain;
                    document.getElementById('calib-ir').value = data.calib_ir_comp;
                    document.getElementById('calib-norm').value = data.calib_norm;
                    updateLedButton(data.led_state);
                    isFirstFetch = false;
                }
            }

            function fetchData() {
                fetch('/fulldata')
                    .then(response => response.json())
                    .then(data => updateUI(data))
                    .catch(error => console.error('Error fetching data:', error));
            }

            fetchData();
            setInterval(fetchData, 1500);
        });
    </script>
</body>
</html>
